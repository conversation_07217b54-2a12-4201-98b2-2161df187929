

struct Solution;
use std::collections::{HashMap, HashSet};
// use std::collections::HashMap;
// Definition for a binary tree node.
#[derive(Debug, PartialEq, Eq)]
pub struct TreeNode {
  pub val: i32,
  pub left: Option<Rc<RefCell<TreeNode>>>,
  pub right: Option<Rc<RefCell<TreeNode>>>,
}

impl TreeNode {
  #[inline]
  pub fn new(val: i32) -> Self {
    TreeNode {
      val,
      left: None,
      right: None
    }
  }
}
use std::rc::Rc;
use std::cell::RefCell;
// Definition for a binary tree node.
// #[derive(Debug, PartialEq, Eq)]
// pub struct TreeNode {
//   pub val: i32,
//   pub left: Option<Rc<RefCell<TreeNode>>>,
//   pub right: Option<Rc<RefCell<TreeNode>>>,
// }
//
// impl TreeNode {
//   #[inline]
//   pub fn new(val: i32) -> Self {
//     TreeNode {
//       val,
//       left: None,
//       right: None
//     }
//   }
// }
// use std::rc::Rc;
// use std::cell::RefCell;
// use std::collections::HashMap;
// use std::collections::HashSet;
impl Solution {
    pub fn make_good(s: String) -> String {
        if s.len() < 2 {
            return s;
        }
        // let mut sv = Vec::new();
        // let mut svv = Vec::new();
        let mut ss:Vec<char> = s.chars().collect();
        let mut a = 0;
        let mut b = 0;
        let mut r = ss.len();
        let mut bc = true;
        while bc {
            bc = false;
            for i in 0..ss.len()-2 {
            if ss[i] < 'Z' {
                if ss[i+1] as u8 == ss[i] as u8 + 26 {
                    i+=2;
                    continue;
                }else{
                    a = i;
                    b = i+1;
                    bc = true;
                    break;
                }
            }else{
                if ss[i+1] as u8 == ss[i] as u8 - 26 {
                    i+=2;
                    continue;
                }else{
                    a = i;
                    b = i+1;
                    bc = true;
                    break;
                }
            }
        }
        let mut ts = Vec::new();
        if bc {
            for i in 0..ss.len() {
                if i == a || i == b {
                    continue;
                }else{
                    ts.push(ss[i]);
                }
            }
            ss = ts;
        }
        }
        
        return ss.iter().collect();
    }
}


fn main() {
    let a = Solution::make_good("leEeetcode".to_string());
    println!("{:?}", a);
}
